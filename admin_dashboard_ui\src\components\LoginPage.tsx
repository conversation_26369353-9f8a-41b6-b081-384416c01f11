import React, { useState, useRef } from "react";
import { API_BASE_URL } from "../apiConfig";

export function LoginPage({ onLogin }: { onLogin: () => void }) {
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState("");
  const [showOtp, setShowOtp] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [otpRequired, setOtpRequired] = useState(false);
  const loadingRef = useRef(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setLoading(true);
    try {
      const res = await fetch(`${API_BASE_URL}/admin/request-otp`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email })
      });
      const data = await res.json();
      if (!res.ok) {
        setError(data.message || "Failed to send OTP");
        return;
      }
      setShowOtp(true);
      setOtpRequired(true);
      setError("");
    } catch (err: any) {
      setError(err.message || "Failed to send OTP");
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (loading || loadingRef.current) return; // Prevent double submit
    loadingRef.current = true;
    setError("");
    setLoading(true);
    try {
      const res = await fetch(`${API_BASE_URL}/admin/verify-otp`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, otp })
      });
      const data = await res.json();
      if (!res.ok) {
        setError(data.message || "Invalid OTP");
        setLoading(false);
        loadingRef.current = false;
        return;
      }
      setOtp("");
      setError("");
      await onLogin();
    } catch (err: any) {
      setError(err.message || "Invalid OTP");
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-accent-400/30 to-neutral-900">
      <form
        onSubmit={showOtp ? handleVerifyOtp : handleSubmit}
        className="bg-neutral-950 shadow-2xl rounded-2xl p-10 flex flex-col gap-6 w-full max-w-md border border-neutral-800"
      >
        <div className="text-3xl font-extrabold text-center bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent mb-2">
          ZiaHR Admin Login
        </div>
        <div className="text-neutral-400 text-center mb-4">Sign in to your admin account</div>
        <input
          type="email"
          placeholder="Admin Email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100 focus:outline-none focus:border-accent-400"
          required
          disabled={otpRequired}
          />
        {showOtp && (
          <input
            type="text"
            placeholder="OTP"
            value={otp}
            onChange={e => setOtp(e.target.value)}
            className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100 focus:outline-none focus:border-accent-400"
            required
            disabled={loading}
          />
        )}
        {error && <div className="text-red-500 text-center font-semibold">{error}</div>}
        <button
          type="submit"
          className="bg-accent-400 hover:bg-accent-600 text-neutral-900 font-bold rounded py-2 text-lg transition"
          disabled={loading}
        >
          {loading ? (showOtp ? "Verifying..." : "Sending OTP...") : (showOtp ? "Verify OTP" : "Send OTP")}
        </button>
        <div className="text-xs text-neutral-500 text-center mt-2">
          Only admin users are allowed. Unauthorized access is prohibited.
        </div>
      </form>
    </div>
  );
} 