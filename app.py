"""
Main application entry point for the Advanced HR Assistant Chatbot.
Optimized for performance with local document processing and vector search.
"""
import os
import time
import asyncio
from pathlib import Path
from dotenv import load_dotenv
import multiprocessing
from src.monitoring import initialize_apm
from admin_dashboard.override_manager import get_override
from admin_dashboard.escalation.live_escalation import escalate_to_slack
from admin_dashboard.twofa_manager import generate_secret, get_qr_code, is_2fa_enabled
import base64
import pyotp
import qrcode
from io import BytesIO
import secrets

# Must be at the very top for Windows multiprocessing
multiprocessing.set_start_method('spawn', force=True)

# Load environment variables from .env file
load_dotenv()

# Validate required environment variables
if not os.getenv("GROQ_API_KEY"):
    raise ValueError("GROQ_API_KEY is not set in the environment or .env file")

# Import all other modules
from flask import Flask, render_template, request, jsonify, session, send_file
from flask_cors import CORS
from markdown import markdown
from src.utils.logger import get_logger
from src.chain.chain_builder import ChainBuilder
from src.conversation.history_manager import HistoryManager
from src.speech.speech_to_text import SpeechToText
from src.speech.text_to_speech import TextToSpeech
from src.document_processing.training_pipeline import TrainingPipeline
from src.user_authentication.user_authorisation import AuthService
from src.utils.email_service import EmailService
from src.config import HR_EMAILS, ENABLE_EMAIL_ESCALATION, GROQ_API_KEY, MOCKAPI_URL
import requests
from src.database.user_db import UserModel
from admin_dashboard.admin_auth_api import AdminUserModel
from src.database.user_db import SessionModel
from src.database.user_db import ConversationModel
from collections import Counter, defaultdict
from datetime import datetime, timedelta

# Initialize logger
logger = get_logger(__name__)

# Global variables for lazy initialization
_chain_builder = None
_history_manager = None
_speech_to_text = None
_text_to_speech = None
_auth_service = None
_email_service = None
_session_history = {}
app_state = {}

# Initialize APM/metrics at startup
apm = initialize_apm()

def get_chain_builder():
    global _chain_builder
    if _chain_builder is None:
        _chain_builder = ChainBuilder()
    return _chain_builder

def get_history_manager():
    global _history_manager
    if _history_manager is None:
        _history_manager = HistoryManager()
    return _history_manager

def get_speech_to_text():
    global _speech_to_text
    if _speech_to_text is None:
        _speech_to_text = SpeechToText()
    return _speech_to_text

def get_text_to_speech():
    global _text_to_speech
    if _text_to_speech is None:
        _text_to_speech = TextToSpeech()
    return _text_to_speech

def get_auth_service():
    global _auth_service
    if _auth_service is None:
        _auth_service = AuthService()
    return _auth_service

def get_email_service():
    global _email_service
    if _email_service is None:
        _email_service = EmailService()
    return _email_service

def run_async_in_sync(coro):
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        else:
            return loop.run_until_complete(coro)
    except RuntimeError:
        return asyncio.run(coro)

def initialize_services():
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true":
        logger.info("Initializing core services...")

        get_chain_builder()
        get_history_manager()

        if ENABLE_EMAIL_ESCALATION and HR_EMAILS:
            get_email_service()

        try:
            pipeline = TrainingPipeline()
            app_state["pipeline"] = pipeline

            # HR file ingestion removed from app startup for production best practices.

        except AttributeError as e:
            logger.warning("🔁 process_hr_files() not found in TrainingPipeline", extra={"error": str(e)})
        except Exception as e:
            import traceback
            error_msg = f"Error during HR document ingestion: {e}\n{traceback.format_exc()}"
            print(error_msg)
            logger.error("Error during HR document ingestion", extra={
                "error": str(e),
                "traceback": traceback.format_exc()
            })


def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__,
               static_folder=os.path.join(os.path.dirname(__file__), "static"),
               template_folder=os.path.join(os.path.dirname(__file__), "templates"))
    app.config['TEMPLATES_AUTO_RELOAD'] = True
    app.jinja_env.cache = {} 
    CORS(app)  # Enable CORS for all routes

    # Set a secret key for session management
    app.secret_key = os.environ.get('FLASK_SECRET_KEY', os.urandom(24))

    @app.route("/")
    def index():
        """Render the main application page."""
        return render_template("index.html")

    @app.route("/api/query", methods=["POST"])
    def query():
        """Process a text query with proper document verification."""
        try:
            data = request.json
            user_query = data["query"]
            device_id = data["device_id"]
            files_info = data.get("files_info", [])

            # Check for override before any LLM/RAG logic
            override_response = get_override(user_query)
            if override_response:
                return jsonify({
                    "response": override_response,
                    "sources": [],
                    "language": "en",
                    "response_time": 0,
                    "document_count": 0,
                    "override": True
                })

            if files_info:
                pipeline = TrainingPipeline()
                for file_info in files_info:
                    if not pipeline.is_file_processed(file_info.get("name")):
                        return jsonify({
                            "error": f"File {file_info.get('name')} not processed. Please re-upload.",
                            "success": False
                        }), 400

            query_start_time = time.time()
            chain_builder = get_chain_builder()

            doc_count = chain_builder.get_vector_database_count()
            if doc_count == 0:
                logger.warning("No documents in vector database")

            # --- LEAVE BALANCE QUERY HANDLING ---
            if any(kw in user_query.lower() for kw in ["leave balance", "how many leaves", "my leave", "leaves in my account"]):
                email = data.get("email")
                employee_id = data.get("employee_id")
                if not email or not employee_id:
                    return jsonify({
                        "error": "Email and employee ID are required for leave balance queries.",
                        "success": False
                    }), 400
                # Call the leave balance logic directly
                from flask import Request
                # Simulate a request context for get_leave_balance
                with app.test_request_context(f"/api/leave-balance?email={email}&employee_id={employee_id}"):
                    return get_leave_balance()
            # --- END LEAVE BALANCE HANDLING ---

            if hasattr(chain_builder, 'run_chain_sync'):
                result = chain_builder.run_chain_sync(user_query, device_id, files_info=files_info)
            else:
                result = run_async_in_sync(chain_builder.run_chain(user_query, device_id, files_info=files_info))

            if isinstance(result, dict) and result.get("sources"):
                logger.info(f"Query used {len(result['sources'])} document sources")
            else:
                logger.warning("Query result contains no document sources - RAG may not be working")

            response_content = result.get("content", "") if isinstance(result, dict) else ""

            # Escalate to Slack if response is empty or unhelpful
            if not response_content.strip() or response_content.strip().startswith("I'm sorry"):
                escalate_to_slack(user_query, device_id)

            if not response_content.strip():
                response_content = "I'm sorry, I couldn't find relevant information in the available documents. Please ensure your documents are properly uploaded and processed."

            # Save conversation to database
            from src.database.conversation_store import ConversationStore
            ConversationStore().save_conversation(
                chat_id=data.get('chat_id'),
                user_query=user_query,
                assistant_response=response_content,
                language=result.get("language", "en") if isinstance(result, dict) else "en",
                device_id=device_id,
                query_start_time=query_start_time,
                response_end_time=time.time()
            )

            final_result = {
                "response": markdown(response_content),
                "sources": result.get("sources", []) if isinstance(result, dict) else [],
                "language": result.get("language", "en") if isinstance(result, dict) else "en",
                "response_time": time.time() - query_start_time,
                "document_count": doc_count
            }

            return jsonify(final_result)

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return jsonify({
                "error": "Query processing failed",
                "message": "Please check if documents are properly uploaded and try again."
            }), 500

    @app.route("/api/speech-to-text", methods=["POST"])
    def speech_to_text_api():
        """Convert speech to text."""
        try:
            audio_data = request.files.get("audio")

            if audio_data:
                temp_path = Path(app.root_path) / "temp_audio.wav"
                audio_data.save(temp_path)
                text = "Audio file processing not implemented yet"
            else:
                text = get_speech_to_text().recognize_speech()

            return jsonify({"text": text})

        except Exception as e:
            logger.error(f"Error in speech-to-text: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/upload-document", methods=["POST"])
    def upload_document():
        """Upload a document (no training)."""
        try:
            if "file" not in request.files:
                return jsonify({"error": "No file uploaded", "success": False}), 400

            file = request.files["file"]
            if file.filename == "":
                return jsonify({"error": "No file selected", "success": False}), 400

            raw_dir = Path(app.root_path) / "data" / "raw"
            os.makedirs(raw_dir, exist_ok=True)
            file_path = raw_dir / file.filename
            file.save(file_path)

            return jsonify({
                "success": True,
                "filename": file.filename,
                "uploaded": True
            })

        except Exception as e:
            logger.error(f"Error uploading document: {e}")
            return jsonify({"error": str(e), "success": False}), 500

    @app.route("/api/start-training", methods=["POST"])
    def start_training():
        """Explicitly start training on a file."""
        try:
            data = request.json
            filename = data.get("filename")
            if not filename:
                return jsonify({"error": "No filename provided", "success": False}), 400

            raw_dir = Path(app.root_path) / "data" / "raw"
            file_path = raw_dir / filename
            if not file_path.exists():
                return jsonify({"error": "File not found", "success": False}), 404

            pipeline = TrainingPipeline()
            success = pipeline.process_file(file_path, force_reprocess=False)

            if not success:
                return jsonify({
                    "error": "Document processing failed",
                    "success": False
                }), 500

            return jsonify({
                "success": True,
                "filename": filename,
                "trained": True
            })

        except Exception as e:
            logger.error(f"Error starting training: {e}")
            return jsonify({"error": str(e), "success": False}), 500

    @app.route("/api/summarize-document", methods=["POST"])
    def summarize_document():
        """Upload a document and return a summary (no training)."""
        try:
            if "file" not in request.files:
                return jsonify({"error": "No file uploaded", "success": False}), 400

            file = request.files["file"]
            if file.filename == "":
                return jsonify({"error": "No file selected", "success": False}), 400

            # Save to temp location
            temp_path = Path(app.root_path) / "data" / "temp" / file.filename
            os.makedirs(temp_path.parent, exist_ok=True)
            file.save(temp_path)

            # Extract text
            document = FileProcessor.process_file(temp_path)
            content = document.get("content", "")

            # Generate summary using your LLM/chain
            chain_builder = get_chain_builder()
            summary = chain_builder.summarize(content)  # You may need to implement this method

            return jsonify({
                "success": True,
                "summary": summary,
                "filename": file.filename
            })

        except Exception as e:
            logger.error(f"Error summarizing document: {e}")
            return jsonify({"error": str(e), "success": False}), 500

    @app.route("/api/clear-history", methods=["POST"])
    def clear_history():
        """Clear conversation history for a device."""
        try:
            data = request.json
            device_id = data["device_id"]

            history_manager = get_history_manager()
            history_manager.clear_history(device_id)

            if device_id in _session_history:
                del _session_history[device_id]

            return jsonify({"success": True})

        except Exception as e:
            logger.error(f"Error clearing history: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/confirm-escalation", methods=["POST"])
    def confirm_escalation():
        """Confirm escalation and send email to HR."""
        try:
            data = request.json
            user_query = data["query"]
            device_id = data["device_id"]

            history = get_history_manager().get_history(device_id)

            if not ENABLE_EMAIL_ESCALATION:
                return jsonify({
                    "success": False,
                    "message": "Email escalation is not enabled"
                }), 400

            if not HR_EMAILS:
                return jsonify({
                    "success": False,
                    "message": "No HR email addresses configured"
                }), 400

            result = get_email_service().send_escalation_email(
                hr_emails=HR_EMAILS,
                user_query=user_query,
                conversation_history=history
            )

            if result["success"]:
                return jsonify({
                    "success": True,
                    "message": "Your question has been escalated to the HR team. They will follow up with you directly."
                })
            else:
                logger.error(f"Error sending escalation email: {result['message']}")
                return jsonify({
                    "success": False,
                    "message": "There was an error escalating your question. Please try again later."
                }), 500

        except Exception as e:
            logger.error(f"Error confirming escalation: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/file-preview", methods=["GET"])
    def file_preview():
        """Get file content for preview."""
        try:
            filename = request.args.get("filename")
            if not filename:
                return jsonify({"error": "No filename provided"}), 400

            raw_dir = Path(app.root_path) / "data" / "raw"
            file_path = raw_dir / filename

            if not file_path.exists():
                return jsonify({"error": f"File {filename} not found"}), 404

            file_extension = file_path.suffix.lower()

            if file_extension in ['.txt', '.md']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return jsonify({
                    "success": True,
                    "content": content,
                    "content_type": "text"
                })
            elif file_extension in ['.pdf', '.docx']:
                return jsonify({
                    "success": True,
                    "file_path": str(file_path),
                    "content_type": file_extension[1:]
                })
            else:
                return jsonify({
                    "error": f"Unsupported file type: {file_extension}"
                }), 400

        except Exception as e:
            logger.error(f"Error getting file preview: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/health/documents", methods=["GET"])
    def document_health_check():
        """Check document processing and vector database health."""
        try:
            pipeline = TrainingPipeline()
            chain_builder = get_chain_builder()

            raw_files = len(list(Path(app.root_path).glob("data/raw/*")))
            processed_files = len(list(Path(app.root_path).glob("data/processed/*")))
            vector_count = chain_builder.get_vector_database_count() if hasattr(chain_builder, 'get_vector_database_count') else 0

            processing_healthy = processed_files > 0 and vector_count > 0

            return jsonify({
                "success": True,
                "raw_files": raw_files,
                "processed_files": processed_files,
                "vector_embeddings": vector_count,
                "processing_healthy": processing_healthy,
                "recommendations": [
                    "Upload documents if none are processed",
                    "Check document processing logs if embeddings are 0",
                    "Restart application if processing appears stuck"
                ] if not processing_healthy else []
            })

        except Exception as e:
            logger.error(f"Document health check failed: {e}")
            return jsonify({
                "success": False,
                "error": str(e)
            }), 500

    @app.route("/api/process-hr-files", methods=["POST"])
    def process_hr_files():
        """Process HR files from a directory."""
        try:
            data = request.json
            directory = data.get("directory", "Hr Files")
            force_reprocess = data.get("force_reprocess", False)

            pipeline = TrainingPipeline()
            num_processed = pipeline.process_hr_files(Path(directory), force_reprocess=force_reprocess)

            return jsonify({
                "success": True,
                "files_processed": num_processed,
                "force_reprocess": force_reprocess
            })

        except Exception as e:
            logger.error(f"Error processing HR files: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/register", methods=["POST"])
    def register():
        """Register a new user."""
        try:
            data = request.json
            email = data.get("email")
            password = data.get("password")
            full_name = data.get("full_name")
            employee_id = data.get("employee_id")

            if not all([email, password, full_name]):
                return jsonify({
                    "success": False,
                    "message": "Email, password, and full name are required"
                }), 400

            # Generate both secrets for new user
            from src.user_authentication.user_authorisation import AuthService
            user_2fa_secret = AuthService().generate_2fa_secret()
            admin_2fa_secret = AuthService().generate_2fa_secret()

            try:
                password_hash = get_auth_service().hash_password(password)
            except ValueError as ve:
                return jsonify({"success": False, "message": str(ve)}), 400

            result = get_auth_service().user_model.create_user(
                email=email,
                password_hash=password_hash,
                full_name=full_name,
                employee_id=employee_id,
                two_fa_secret_user=user_2fa_secret,
                two_fa_secret_admin=admin_2fa_secret,
                role='user'
            )

            if not result.get('success'):
                return jsonify({"success": False, "message": result.get('message', 'Failed to register user')}), 400

            # Return both QR URLs for setup (optional, for demo)
            user_qr = AuthService().get_2fa_qr_url(email, user_2fa_secret, role='user')
            admin_qr = AuthService().get_2fa_qr_url(email, admin_2fa_secret, role='admin')
            return jsonify({
                "success": True,
                "message": "User registered successfully",
                "user_id": result.get('user_id'),
                "2fa_qr_url_user": user_qr,
                "2fa_qr_url_admin": admin_qr
            }), 201

        except Exception as e:
            logger.error(f"Error registering user: {e}")
            return jsonify({
                "success": False,
                "message": f"An error occurred: {str(e)}"
            }), 500

    @app.route('/api/user-2fa-setup', methods=["POST"])
    def user_2fa_setup():
        data = request.json
        email = data.get("email")
        if not email:
            return jsonify({"success": False, "message": "Email is required"}), 400
        user = UserModel().get_user_by_email(email)
        if not user:
            return jsonify({"success": False, "message": "User not found"}), 404
        secret = user.get("two_fa_secret_user")
        if not secret:
            # Optionally generate and save a new secret here
            secret = AuthService().generate_2fa_secret()
            # You could update the user in DB here if needed
        qr_url = AuthService().get_2fa_qr_url(email, secret, role='user')
        return jsonify({"success": True, "qr_url": qr_url})

    @app.route("/api/user", methods=["GET"])
    def get_user():
        """Get the current user's information."""
        try:
            user_id = session.get("user_id")
            if not user_id:
                return jsonify({
                    "success": False,
                    "message": "Not authenticated"
                }), 401

            user = get_auth_service().user_model.get_user_by_id(user_id)
            if not user:
                session.clear()
                return jsonify({
                    "success": False,
                    "message": "User not found"
                }), 404

            return jsonify({
                "success": True,
                "user": {
                    "id": user["id"],
                    "email": user["email"],
                    "full_name": user["full_name"],
                    "company_name": user["company_name"],
                    "employee_id": user.get("employee_id")
                }
            }), 200

        except Exception as e:
            logger.error(f"Error getting user: {e}")
            return jsonify({
                "success": False,
                "message": f"An error occurred: {str(e)}"
            }), 500

    @app.route("/api/hr-representatives", methods=["GET"])
    def get_hr_representatives():
        """Get list of HR representatives."""
        try:
            hr_reps = [
                {"id": "hr1", "name": "John Smith", "email": "<EMAIL>", "department": "HR Operations"},
                {"id": "hr2", "name": "Sarah Johnson", "email": "<EMAIL>", "department": "Employee Relations"},
                {"id": "hr3", "name": "Michael Brown", "email": "<EMAIL>", "department": "Benefits"}
            ]
            return jsonify({"success": True, "representatives": hr_reps})
        except Exception as e:
            logger.error(f"Error getting HR representatives: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/submit-escalation", methods=["POST"])
    def submit_escalation():
        """Submit a new HR escalation."""
        try:
            data = request.json
            required_fields = ["hrPerson", "issueType", "issueDescription", "priority"]

            for field in required_fields:
                if field not in data:
                    return jsonify({
                        "success": False,
                        "message": f"Missing required field: {field}"
                    }), 400

            user_details = session.get("user_details", {})
            if not user_details:
                return jsonify({
                    "success": False,
                    "message": "User not authenticated"
                }), 401

            escalation_data = {
                "user_id": user_details.get("id"),
                "user_name": user_details.get("name"),
                "user_email": user_details.get("email"),
                "hr_person": data["hrPerson"],
                "issue_type": data["issueType"],
                "description": data["issueDescription"],
                "priority": data["priority"],
                "status": "pending",
                "created_at": time.time()
            }

            logger.info(f"New escalation created: {escalation_data}")

            if ENABLE_EMAIL_ESCALATION:
                email_service = get_email_service()
                hr_email = next((rep["email"] for rep in get_hr_representatives().json["representatives"]
                               if rep["id"] == data["hrPerson"]), None)

                if hr_email:
                    email_service.send_escalation_email(
                        hr_emails=[hr_email],
                        user_query=f"New HR Escalation: {data['issueType']} - {data['issueDescription']}",
                        conversation_history=[{
                            "user_query": f"Priority: {data['priority']}\nIssue Type: {data['issueType']}\nDescription: {data['issueDescription']}",
                            "assistant_response": f"Escalated to: {data['hrPerson']}"
                        }]
                    )

            return jsonify({
                "success": True,
                "message": "Your issue has been escalated to HR. They will contact you shortly.",
                "escalation_id": f"ESC-{int(time.time())}"
            })

        except Exception as e:
            logger.error(f"Error submitting escalation: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/chats/<chat_id>", methods=["GET"])
    def get_chat(chat_id):
        """Get paginated messages for a specific chat."""
        try:
            page = int(request.args.get("page", 1))
            page_size = int(request.args.get("page_size", 20))

            history_manager = get_history_manager()
            messages = history_manager.get_chat_messages(chat_id, page, page_size)

            total_messages = history_manager.get_chat_message_count(chat_id)

            return jsonify({
                "success": True,
                "messages": messages,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_messages": total_messages,
                    "total_pages": (total_messages + page_size - 1) // page_size
                }
            })

        except Exception as e:
            logger.error(f"Error getting chat messages: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route("/api/chats/<chat_id>/count", methods=["GET"])
    def get_chat_message_count_api(chat_id):
        """Get total number of messages for a specific chat."""
        try:
            history_manager = get_history_manager()
            total_messages = history_manager.get_chat_message_count(chat_id)
            return jsonify({
                "success": True,
                "count": total_messages
            })
        except Exception as e:
            logger.error(f"Error getting chat message count: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route('/api/queries')
    def get_queries():
        # Dummy data since no query_logs.json exists
        queries = [
            {"id": 1, "text": "How to apply for leave?", "time": "2 min ago"},
            {"id": 2, "text": "Payroll status for June", "time": "10 min ago"},
            {"id": 3, "text": "Update contact info", "time": "1 hr ago"},
        ]
        return jsonify(queries)

    @app.route('/api/chatlogs')
    def get_chatlogs():
        # Dummy data since no chat_logs.json exists
        chats = [
            {"id": 1, "user": "Alice", "msg": "Can I get my payslip?", "tag": "Payroll", "time": "Today"},
            {"id": 2, "user": "Bob", "msg": "Leave balance?", "tag": "Leave", "time": "Yesterday"},
        ]
        return jsonify(chats)

    @app.route('/api/overrides')
    def get_overrides():
        overrides = [
            {"id": 1, "pattern": "leave policy", "response": "Refer to HR portal"},
            {"id": 2, "pattern": "salary slip", "response": "Contact payroll"},
        ]
        return jsonify(overrides)

    @app.route('/api/upload', methods=['POST'])
    def upload_doc():
        file = request.files.get('file')
        if not file or file.filename == '':
            return jsonify({'status': 'error', 'message': 'No file selected'}), 400
        # Save file logic here (not implemented)
        return jsonify({'status': 'success', 'filename': file.filename})

    @app.route('/api/all-chats', methods=['GET'])
    def get_all_chats():
        """
        Get all chatbot conversations, with optional filters for user/session (device_id), date range, and pagination.
        Query params:
            device_id: filter by user/session (optional)
            start_date: ISO string (optional)
            end_date: ISO string (optional)
            limit: int (default 100)
            offset: int (default 0)
        Returns:
            JSON list of conversations
        """
        device_id = request.args.get('device_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        try:
            limit = int(request.args.get('limit', 100))
            offset = int(request.args.get('offset', 0))
        except ValueError:
            limit = 100
            offset = 0
        from src.database.conversation_store import ConversationStore
        store = ConversationStore()
        conversations = store.get_all_conversations(device_id=device_id, start_date=start_date, end_date=end_date, limit=limit, offset=offset)
        return jsonify(conversations)

    @app.route('/api/chat-trends', methods=['GET'])
    @app.route('/chat-trends', methods=['GET'])
    def chat_trends():
        """Returns chat volume per day and 7d average for the last 7 days."""
        conv_model = ConversationModel()
        today = datetime.now().date()
        days = [(today - timedelta(days=i)) for i in range(6, -1, -1)]
        day_strs = [d.strftime('%Y-%m-%d') for d in days]
        chats_per_day = Counter()
        all_convs = conv_model.get_conversations(limit=10000)
        for c in all_convs:
            dt = c['query_timestamp'][:10] if 'query_timestamp' in c else None
            if dt in day_strs:
                chats_per_day[dt] += 1
        avg = sum(chats_per_day.values()) / 7 if chats_per_day else 0
        data = [{"date": d, "chats": chats_per_day[d], "avg": round(avg, 2)} for d in day_strs]
        return jsonify(data)

    @app.route('/api/chat-types', methods=['GET'])
    @app.route('/chat-types', methods=['GET'])
    def chat_types():
        """Returns sentiment/type distribution (mocked as language for now)."""
        conv_model = ConversationModel()
        all_convs = conv_model.get_conversations(limit=10000)
        type_counter = Counter(c.get('language', 'en') for c in all_convs)
        data = [{"name": k, "value": v} for k, v in type_counter.items()]
        return jsonify(data)

    @app.route('/api/queries', methods=['GET'])
    @app.route('/queries', methods=['GET'])
    def queries():
        """Returns top user questions."""
        conv_model = ConversationModel()
        all_convs = conv_model.get_conversations(limit=10000)
        question_counter = Counter(c.get('user_query', '') for c in all_convs)
        last_asked = {}
        intent_map = {}
        for c in all_convs:
            q = c.get('user_query', '')
            last_asked[q] = c.get('query_timestamp', '')
            intent_map[q] = c.get('intent') or q.split()[0]
        top = question_counter.most_common(10)
        data = [{
            "question": q,
            "count": count,
            "lastAsked": last_asked[q],
            "intent": intent_map[q]
        } for q, count in top]
        return jsonify(data)

    @app.route('/api/login', methods=["POST"])
    @app.route('/login', methods=["POST"])
    def login():
        try:
            data = request.json
            email = data.get("email")
            password = data.get("password")
            two_fa_code = data.get("two_fa_code")
            if not email or not password:
                return jsonify({"success": False, "message": "Email and password are required"}), 400
            from src.user_authentication.user_authorisation import AuthService
            auth_service = AuthService()
            result = auth_service.login_user(email, password, two_fa_code)
            if result.get("success"):
                session["user_id"] = result["user"]["id"]
                session["email"] = result["user"]["email"]
                return jsonify(result), 200
            else:
                # If 2FA required, return that message
                if result.get("message") == "2FA code required":
                    return jsonify(result), 401
                return jsonify(result), 401
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return jsonify({"success": False, "message": f"An error occurred: {str(e)}"}), 500

    @app.route('/api/logout', methods=['POST'])
    def logout():
        session.clear()
        return jsonify({"success": True})

    @app.route("/api/leave-balance", methods=["GET"])
    def get_leave_balance():
        email = request.args.get("email")
        employee_id = request.args.get("employee_id")
        user_id = session.get("user_id")
        print(f"[DEBUG] /api/leave-balance: user_id={user_id}, email(param)={email}, employee_id(param)={employee_id}, session={dict(session)}")
        if not email or not employee_id:
            # Try to get from session user_id
            if user_id:
                user = get_auth_service().user_model.get_user_by_id(user_id)
                print(f"[DEBUG] /api/leave-balance: user from DB={user}")
                if user:
                    email = email or user.get("email")
                    employee_id = employee_id or user.get("employee_id")
        if not email or not employee_id:
            print(f"[DEBUG] /api/leave-balance: MISSING email or employee_id after all attempts. Returning error.")
            return jsonify({
                "success": False,
                "message": "Email and employee ID are required. Please complete your info in Settings."
            }), 400
        try:
            url = f"{MOCKAPI_URL}?employee_id={employee_id}&email={email}"
            resp = requests.get(url, timeout=10)
            if resp.status_code == 200:
                data = resp.json()
                # --- PATCH: handle array response from mockapi ---
                if isinstance(data, list) and data:
                    data = data[0]
                if not data or not any(k in data for k in ("paid_leave", "sick_leave", "casual_leave")):
                    return jsonify({
                        "success": False,
                        "message": "We couldn’t find your leave records. Please check your info in Settings and enter correct information."
                    }), 404
                # --- Add chatbot-friendly response field ---
                response_msg = f"Hi {data.get('name', '')}, your leave balance is: Paid Leave: {data.get('paid_leave', 0)}, Sick Leave: {data.get('sick_leave', 0)}, Casual Leave: {data.get('casual_leave', 0)}."
                return jsonify({
                    "success": True,
                    "response": response_msg,
                    "paid_leave": data.get("paid_leave", 0),
                    "sick_leave": data.get("sick_leave", 0),
                    "casual_leave": data.get("casual_leave", 0),
                    "name": data.get("name", "")
                })
            else:
                return jsonify({
                    "success": False,
                    "message": "We couldn’t find your leave records. Please check your info in Settings and enter correct information."
                }), 404
        except Exception as e:
            return jsonify({
                "success": False,
                "message": "Something went wrong while fetching your leave data. Try again later.",
                "error": str(e)
            }), 500

    @app.route('/api/roles', methods=['GET'])
    def get_roles():
        # Mock roles for demo; replace with DB query in production
        return jsonify([
            {"id": 1, "name": "Superadmin", "level": 3},
            {"id": 2, "name": "Admin", "level": 2},
            {"id": 3, "name": "Viewer", "level": 1}
        ])

    user_model = UserModel()
    admin_user_model = AdminUserModel()

    @app.route('/api/admin-users', methods=['GET'])
    def get_admin_users():
        users = admin_user_model.get_admin_users()
        return jsonify([
            {
                "id": u.get("id"),
                "name": u.get("full_name", u.get("name", "")),
                "email": u.get("email", ""),
                "role": u.get("role", ""),
                "role_level": u.get("role_level", 1),
                "tenant_id": u.get("tenant_id", None),
                "created_at": u.get("created_at", None),
                "last_login": u.get("last_login", None),
            }
            for u in users
        ])

    @app.route('/api/admin-users', methods=['POST'])
    def add_admin_user():
        data = request.json
        users = admin_user_model.get_admin_users()
        if any(u['email'].lower() == data['email'].lower() for u in users):
            return jsonify({"success": False, "message": "Email already exists."}), 400
        user = {
            "email": data["email"],
            "full_name": data["name"],
            "role": data["role"],
            "role_level": 3 if data["role"] == "Superadmin" else 2 if data["role"] == "Admin" else 1,
            "tenant_id": data.get("tenant_id"),
            "created_at": "2024-06-01T12:00:00Z",
            "last_login": None
        }
        result = admin_user_model.create_user(
            email=user["email"],
            full_name=user["full_name"],
            role=user["role"],
            role_level=user["role_level"],
            tenant_id=user["tenant_id"],
            created_at=user["created_at"],
            last_login=user["last_login"]
        )
        if result.get("success"):
            return jsonify({"success": True, "id": result["user_id"]})
        else:
            return jsonify({"success": False, "message": result.get("message", "Failed to add user")}), 500

    @app.route('/api/admin-users/<int:user_id>', methods=['PATCH'])
    def update_admin_user(user_id):
        data = request.json
        # Validate role_id
        valid_roles = [
            {"id": 1, "name": "Superadmin"},
            {"id": 2, "name": "Admin"},
            {"id": 3, "name": "Viewer"}
        ]
        if "role_id" not in data:
            return jsonify({"success": False, "message": "Missing role_id in request."}), 400
        role_entry = next((r for r in valid_roles if r["id"] == data["role_id"]), None)
        if not role_entry:
            return jsonify({"success": False, "message": f"Invalid role_id: {data['role_id']}"}), 400
        role = role_entry["name"]
        try:
            success = admin_user_model.update_user(
                user_id=user_id,
                full_name=data.get("name"),
                role=role,
                tenant_id=data.get("tenant_id")
            )
            if success:
                return jsonify({"success": True})
            else:
                print(f"[ERROR] Failed to update admin user: user_id={user_id}, data={data}")
                return jsonify({"success": False, "message": "Failed to update admin user. Check user_id and database."}), 500
        except Exception as e:
            import traceback
            print(f"[EXCEPTION] Error updating admin user: user_id={user_id}, data={data}\n{traceback.format_exc()}")
            return jsonify({"success": False, "message": f"Exception: {str(e)}"}), 500

    @app.route('/api/admin-users/<int:user_id>', methods=['DELETE'])
    def delete_admin_user(user_id):
        success = admin_user_model.delete_user(user_id)
        if success:
            return jsonify({"success": True})
        else:
            return jsonify({"success": False, "message": "Failed to delete user"}), 500

    @app.route('/api/admin-users/me', methods=['GET'])
    def get_admin_user_me():
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"id": None, "name": "", "email": "", "role": "", "role_level": 1, "tenant_id": None, "created_at": None, "last_login": None}), 200
        user = admin_user_model.get_admin_user_by_id(user_id)
        if not user:
            return jsonify({"id": None, "name": "", "email": "", "role": "", "role_level": 1, "tenant_id": None, "created_at": None, "last_login": None}), 200
        return jsonify({
            "id": user.get("id"),
            "name": user.get("full_name", user.get("name", "")),
            "email": user.get("email", ""),
            "role": user.get("role", ""),
            "role_level": user.get("role_level", 1),
            "tenant_id": user.get("tenant_id", None),
            "created_at": user.get("created_at", None),
            "last_login": user.get("last_login", None)
        })

    @app.route('/api/sessions/active', methods=['GET'])
    def get_active_sessions():
        user_type = request.args.get('user_type', 'admin')
        sessions = SessionModel().get_active_sessions(user_type=user_type)
        return jsonify({'success': True, 'sessions': sessions})

    @app.route('/api/sessions/history', methods=['GET'])
    def get_session_history():
        user_type = request.args.get('user_type', 'admin')
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))
        # Collect filters from query params
        filters = {}
        for key in ['user_id', 'ip_address', 'auth_method', 'success']:
            value = request.args.get(key)
            if value is not None:
                filters[key] = value
        sessions = SessionModel().get_session_history(user_type=user_type, limit=limit, offset=offset, filters=filters)
        return jsonify({'success': True, 'logs': sessions})

    @app.route('/api/sessions/locations', methods=['GET'])
    def get_session_locations():
        user_type = request.args.get('user_type', 'admin')
        locations = SessionModel().get_session_locations(user_type=user_type)
        return jsonify({'success': True, 'locations': locations})

    @app.route('/api/sessions/anomalies', methods=['GET'])
    def get_session_anomalies():
        user_type = request.args.get('user_type', 'admin')
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))
        anomalies = SessionModel().get_session_anomalies(user_type=user_type, limit=limit, offset=offset)
        return jsonify({'success': True, 'anomalies': anomalies})

    @app.route('/api/chat-analytics/live', methods=['GET'])
    def chat_analytics_live():
        """
        Returns live analytics for the Insights dashboard tab.
        Structure matches frontend expectations:
          - KPIs: total_queries, avg_sentiment, active_users, unique_questions
          - Top Intents: [{ intent, count }]
          - Trending Topics: [{ topic, trend: [int] }]
          - Sentiment Distribution: [{ sentiment, count }]
          - Top Questions: [{ question, count }]
        """
        try:
            # --- KPIs ---
            kpis = {
                "total_queries": 234,
                "avg_sentiment": 4.2,
                "active_users": 57,
                "unique_questions": 41
            }
            # --- Top Intents ---
            top_intents = [
                {"intent": "Leave Balance", "count": 120},
                {"intent": "Payslip", "count": 90},
                {"intent": "Attendance", "count": 70},
                {"intent": "Policy", "count": 50},
                {"intent": "Holiday", "count": 35}
            ]
            # --- Trending Topics (trend is a list of counts over time) ---
            trending_topics = [
                {"topic": "Leave Balance", "trend": [30, 32, 35, 40, 45, 50, 48]},
                {"topic": "Payslip", "trend": [20, 22, 25, 28, 30, 32, 31]},
                {"topic": "Attendance", "trend": [15, 16, 18, 20, 22, 23, 22]}
            ]
            # --- Sentiment Distribution ---
            sentiment_distribution = [
                {"sentiment": "Positive", "count": 180},
                {"sentiment": "Neutral", "count": 40},
                {"sentiment": "Negative", "count": 14}
            ]
            # --- Top Questions ---
            top_questions = [
                {"question": "How many leaves do I have?", "count": 34},
                {"question": "Show my payslip", "count": 28},
                {"question": "What is the holiday policy?", "count": 19},
                {"question": "Mark attendance", "count": 15},
                {"question": "How to apply for leave?", "count": 12}
            ]
            return jsonify({
                **kpis,
                "top_intents": top_intents,
                "trending_topics": trending_topics,
                "sentiment_distribution": sentiment_distribution,
                "top_questions": top_questions
            })
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    @app.route('/api/chatlogs', methods=['GET'])
    def get_chat_logs():
        print("HIT /api/chatlogs")
        try:
            from src.database.user_db import ConversationModel
            conv_model = ConversationModel()
            all_convs = conv_model.get_conversations(limit=10000) # Fetch all conversations
            # Format the conversations to match the ChatLog type in ChatLogs.tsx
            formatted_logs = []
            for conv in all_convs:
                formatted_logs.append({
                    "user_query": conv["user_query"],
                    "assistant_response": conv["assistant_response"],
                    "language": conv["language"],
                    "timestamp": conv["query_timestamp"]
                })
            print("Returning chat logs:", formatted_logs)
            return jsonify(formatted_logs)
        except Exception as e:
            print("ERROR in /api/chatlogs:", e)
            return jsonify({"error": str(e)}), 500

    return app

if __name__ == "__main__":
    try:
        initialize_services()
        flask_app = create_app()
        flask_app.run(host="0.0.0.0", port=5051, debug=True)
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        raise
